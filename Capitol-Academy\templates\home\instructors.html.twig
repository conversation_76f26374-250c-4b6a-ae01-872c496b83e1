{% extends 'base.html.twig' %}

{% block title %}Instructors - Capitol Academy{% endblock %}

{% block body %}
<div class="container-fluid p-0">
    <!-- Hero Section -->
    <section class="hero-section py-5" style="background: linear-gradient(135deg, #f6f7f9 0%, #e9ecef 100%); min-height: 70vh; margin: 0; padding: 0;">
        <div class="container">
            <div class="row align-items-center justify-content-center text-center">
                <div class="col-lg-10">
                    <div class="hero-content">
                        <h1 class="display-4 fw-bold mb-4">
                            <span style="color: #011a2d;">Meet</span> <span style="color: #a90418;">Capitol Academy</span> <span style="color: #011a2d;">Instructors</span>
                        </h1>
                        <p class="lead mb-5" style="color: #495057; line-height: 1.6; font-size: 1.5rem; font-family: '<PERSON><PERSON><PERSON>', Arial, sans-serif;">
                            We offers you the opportunity to grow for personal and professional growth as a trader. Our programs prepare beginner to seasoned traders to understand and master the different technical Analysis tools.
                        </p>

                        <!-- Instructor Cards -->
                        {% if instructors %}
                            <div class="row g-4" style="margin-left: 0; margin-right: 0; width: 100%;">
                                {% for instructor in instructors %}
                                    <div class="col-lg-4 col-md-6" style="padding-left: 0; padding-right: 15px;">
                                        <div class="card border-0 shadow-lg instructor-card d-flex flex-column" style="transition: all 0.3s ease; border-radius: 20px; height: 340px; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border: 1px solid rgba(1, 26, 45, 0.1);">
                                            <div class="card-body text-center p-4 d-flex flex-column" style="padding-top: 2rem !important; padding-bottom: 2rem !important;">
                                                <!-- Instructor Image -->
                                                <div class="position-relative mb-3">
                                                    <div class="instructor-image-container mx-auto" style="width: 100px; height: 100px; border-radius: 50%; overflow: hidden; border: 3px solid #011a2d; position: relative; box-shadow: 0 4px 15px rgba(1, 26, 45, 0.2);">
                                                        {% if instructor.profileImage %}
                                                            <img src="{{ asset('uploads/instructors/' ~ instructor.profileImage) }}"
                                                                 alt="{{ instructor.name }}"
                                                                 class="w-100 h-100"
                                                                 style="object-fit: cover;"
                                                                 onerror="this.src='{{ asset('images/instructors/instructor-default-pp.png') }}'">
                                                        {% else %}
                                                            <img src="{{ asset('images/instructors/instructor-default-pp.png') }}"
                                                                 alt="{{ instructor.name }}"
                                                                 class="w-100 h-100"
                                                                 style="object-fit: cover;">
                                                        {% endif %}
                                                    </div>
                                                </div>

                                                <!-- Instructor Info -->
                                                <h5 class="fw-bold mb-2" style="color: #011a2d; font-family: 'Montserrat', sans-serif; font-size: 1.1rem;">{{ instructor.name }}</h5>
                                                {% if instructor.specialization %}
                                                    <p class="text-muted mb-3" style="font-size: 0.85rem; font-family: 'Calibri', Arial, sans-serif; line-height: 1.4;">{{ instructor.specialization }}</p>
                                                {% endif %}

                                                <!-- Preview Icon Button -->
                                                <div class="mt-auto">
                                                    <button class="btn preview-btn d-flex align-items-center justify-content-center mx-auto"
                                                            style="border: 2px solid #011a2d; color: #011a2d; background: transparent; border-radius: 50%; width: 45px; height: 45px; transition: all 0.3s ease;"
                                                            data-instructor-id="{{ instructor.id }}"
                                                            data-instructor-name="{{ instructor.name }}"
                                                            data-instructor-specialization="{{ instructor.specialization }}"
                                                            data-instructor-bio="{{ instructor.bio }}"
                                                            data-instructor-qualifications="{{ instructor.qualifications|join(', ') }}"
                                                            data-instructor-achievements="{{ instructor.achievements|join(', ') }}"
                                                            data-instructor-email="{{ instructor.email }}"
                                                            data-instructor-linkedin="{{ instructor.linkedinUrl }}"
                                                            data-instructor-image="{% if instructor.profileImage %}{{ asset('uploads/instructors/' ~ instructor.profileImage) }}{% else %}{{ asset('images/instructors/instructor-default-pp.png') }}{% endif %}"
                                                            onmouseover="this.style.background='#011a2d'; this.style.color='white'; this.style.transform='scale(1.1)'"
                                                            onmouseout="this.style.background='transparent'; this.style.color='#011a2d'; this.style.transform='scale(1)'">
                                                        <i class="fas fa-eye" style="font-size: 1.1rem;"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <div class="text-center py-5">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <h4 class="text-muted">No instructors available at the moment</h4>
                                <p class="text-muted">Please check back later for our expert instructors.</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Section 4: Contact Form (Two-Column Layout with Background Image) -->
    <section class="position-relative" style="background: url('{{ asset('images/backgrounds/Background Any Question Contact Us.png') }}') center/cover; height: 100vh; margin: 0; padding: 0; display: flex; align-items: center;">
        <div class="container h-100">
            <div class="row align-items-center h-100">
                <div class="col-lg-6">
                    <!-- Left Column: Title and Text -->
                    <div class="text-white pe-lg-5" style="margin-right: 3rem;">
                        <h2 class="h1 fw-bold mb-4" style="color: white; font-family: 'Montserrat', sans-serif; font-size: 2.3rem;">
                            <span>Any Question ?</span> <span>Contact us</span>
                        </h2>
                        <p class="lead mb-4" style="font-size: 1.4rem; line-height: 1.8; color: white; font-family: 'Calibri', Arial, sans-serif;">
                            Have a question or need some help ? Drop us a message below and we will be in touch as soon as possible.
                        </p>
                    </div>
                </div>
                <div class="col-lg-5 offset-lg-1">
                    <!-- Right Column: Contact Form (Narrower) -->
                    <div class="card border-0 shadow-lg" style="background: rgba(255, 255, 255, 0.05); backdrop-filter: blur(20px); border-radius: 15px; border: 1px solid rgba(255, 255, 255, 0.15);">
                        <div class="card-body p-4">
                            <form action="{{ path('app_contact_unified') }}" method="POST" class="needs-validation" novalidate>
                                <input type="hidden" name="source_page" value="instructors">

                                <div class="mb-3">
                                    <input type="text" name="name" class="form-control glassmorphism-input"
                                           placeholder="Name ..." required
                                           style="border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide your name.</div>
                                </div>

                                <div class="mb-3">
                                    <input type="email" name="email" class="form-control glassmorphism-input"
                                           placeholder="Email ..." required
                                           style="border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px;">
                                    <div class="invalid-feedback">Please provide a valid email address.</div>
                                </div>

                                <div class="mb-3">
                                    <textarea name="message" class="form-control glassmorphism-input" rows="4"
                                              placeholder="Message ..." required
                                              style="border: 1px solid rgba(255, 255, 255, 0.25); border-radius: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(10px); color: white; padding: 12px 15px; resize: vertical;"></textarea>
                                    <div class="invalid-feedback">Please provide your message.</div>
                                </div>

                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="privacyConsent" name="privacy_consent" checked required>
                                        <label class="form-check-label" for="privacyConsent" style="font-size: 0.85rem; line-height: 1.4; color: rgba(255, 255, 255, 0.8);">
                                            By completing this form, I give my consent for the processing of my personal data for the purpose of providing the requested service.
                                            Personal data will be processed only for this purpose and will be protected according to our Privacy Policy and Terms and Conditions.
                                        </label>
                                    </div>
                                </div>

                                <div class="d-grid">
                                    <button type="submit" class="btn"
                                            style="background: #28a745; border: 2px solid white; padding: 12px; border-radius: 8px; font-weight: 600; color: white; transition: all 0.3s ease;"
                                            onmouseover="this.style.background='#218838'; this.style.transform='translateY(-2px)'"
                                            onmouseout="this.style.background='#28a745'; this.style.transform='translateY(0)'">
                                        Send Now
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<!-- Enhanced Instructor Preview Popup -->
<div id="instructorPopup" class="instructor-popup" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.7); z-index: 9999; backdrop-filter: blur(8px);">
    <div class="popup-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border-radius: 25px; padding: 30px; max-width: 680px; width: 90%; box-shadow: 0 25px 80px rgba(1, 26, 45, 0.3); border: 1px solid rgba(1, 26, 45, 0.1);">
        <div class="text-center">
            <!-- Enhanced Close Button -->
            <button class="btn-close popup-close" style="position: absolute; top: 20px; right: 20px; background: rgba(1, 26, 45, 0.1); border: none; font-size: 20px; cursor: pointer; color: #011a2d; width: 35px; height: 35px; border-radius: 50%; display: flex; align-items: center; justify-content: center; transition: all 0.3s ease;" onmouseover="this.style.background='#011a2d'; this.style.color='white'" onmouseout="this.style.background='rgba(1, 26, 45, 0.1)'; this.style.color='#011a2d'">&times;</button>

            <!-- Enhanced Profile Picture -->
            <div class="mb-3">
                <img id="popupImage" src="" alt="" class="rounded-circle" style="width: 120px; height: 120px; object-fit: cover; border: 4px solid #011a2d; box-shadow: 0 8px 25px rgba(1, 26, 45, 0.2);">
            </div>

            <!-- Enhanced Name and Specialization -->
            <h3 id="popupName" class="fw-bold mb-1" style="color: #011a2d; font-family: 'Montserrat', sans-serif; font-size: 1.4rem;"></h3>
            <p id="popupSpecialization" class="text-muted mb-3" style="font-family: 'Calibri', Arial, sans-serif; font-size: 0.95rem; font-style: italic;"></p>

            <!-- Enhanced Biography -->
            <div class="mb-3 text-start">
                <h6 class="fw-bold mb-2" style="color: #011a2d; font-family: 'Montserrat', sans-serif; border-bottom: 2px solid #011a2d; padding-bottom: 3px; display: inline-block; font-size: 0.9rem;">Biography</h6>
                <p id="popupBio" class="text-muted" style="line-height: 1.5; font-family: 'Calibri', Arial, sans-serif; font-size: 0.9rem;"></p>
            </div>

            <!-- Enhanced Qualifications -->
            <div id="popupQualificationsSection" class="mb-3 text-start">
                <h6 class="fw-bold mb-2" style="color: #011a2d; font-family: 'Montserrat', sans-serif; border-bottom: 2px solid #971020; padding-bottom: 3px; display: inline-block; font-size: 0.9rem;">Qualifications</h6>
                <p id="popupQualifications" class="text-muted" style="line-height: 1.5; font-family: 'Calibri', Arial, sans-serif; font-size: 0.9rem;"></p>
            </div>

            <!-- Enhanced Achievements -->
            <div id="popupAchievementsSection" class="mb-3 text-start">
                <h6 class="fw-bold mb-2" style="color: #011a2d; font-family: 'Montserrat', sans-serif; border-bottom: 2px solid #45403f; padding-bottom: 3px; display: inline-block; font-size: 0.9rem;">Achievements</h6>
                <p id="popupAchievements" class="text-muted" style="line-height: 1.5; font-family: 'Calibri', Arial, sans-serif; font-size: 0.9rem;"></p>
            </div>

            <!-- Enhanced Contact Buttons -->
            <div class="d-flex justify-content-center gap-3 mt-3">
                <a id="popupEmailBtn" href="" class="btn" style="border: 2px solid #011a2d; color: #011a2d; background: transparent; border-radius: 25px; padding: 10px 20px; font-family: 'Montserrat', sans-serif; font-weight: 600; transition: all 0.3s ease;" onmouseover="this.style.background='#011a2d'; this.style.color='white'" onmouseout="this.style.background='transparent'; this.style.color='#011a2d'">
                    <i class="fas fa-envelope me-2"></i>Email
                </a>
                <a id="popupLinkedinBtn" href="" target="_blank" class="btn" style="border: 2px solid #971020; color: #971020; background: transparent; border-radius: 25px; padding: 10px 20px; font-family: 'Montserrat', sans-serif; font-weight: 600; transition: all 0.3s ease;" onmouseover="this.style.background='#971020'; this.style.color='white'" onmouseout="this.style.background='transparent'; this.style.color='#971020'">
                    <i class="fab fa-linkedin-in me-2"></i>LinkedIn
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.instructor-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(1, 26, 45, 0.2) !important;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.instructor-image-container:hover {
    transform: scale(1.05);
    transition: transform 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: #011a2d !important;
    border-color: #011a2d !important;
    color: white !important;
}

/* Contact Form Specific Styles */
.glassmorphism-input {
    color: white !important;
}

.glassmorphism-input:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none !important;
    color: white !important;
}

.glassmorphism-input::placeholder {
    color: rgba(255,255,255,0.7) !important;
    opacity: 1 !important;
}

.form-control:focus {
    border-color: rgba(255,255,255,0.5) !important;
    box-shadow: 0 0 0 0.2rem rgba(255,255,255,0.25) !important;
    outline: none;
}

.form-control::placeholder {
    color: rgba(255,255,255,0.7);
    opacity: 1;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* Form Validation Styles */
.was-validated .form-control:valid {
    border-color: #28a745;
}

.was-validated .form-control:invalid {
    border-color: #dc3545;
}

@media (max-width: 768px) {
    .display-4 {
        font-size: 2.5rem;
    }

    .instructor-image-container {
        width: 100px !important;
        height: 100px !important;
    }

    /* Adjust contact section height for mobile */
    section[style*="calc(100vh - 80px)"] {
        height: auto !important;
        min-height: 100vh !important;
    }
}
</style>
{% endblock %}

{% block javascripts %}
<script>
// Form validation and enhancement
document.addEventListener('DOMContentLoaded', function() {
    // Instructor Popup Functionality
    const popup = document.getElementById('instructorPopup');
    const previewBtns = document.querySelectorAll('.preview-btn');
    const closeBtn = document.querySelector('.popup-close');

    // Show popup ONLY on preview button click
    previewBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            showPopup.call(this);
        });
    });

    function showPopup() {
        const instructorData = {
            name: this.dataset.instructorName,
            specialization: this.dataset.instructorSpecialization,
            bio: this.dataset.instructorBio,
            qualifications: this.dataset.instructorQualifications,
            achievements: this.dataset.instructorAchievements,
            email: this.dataset.instructorEmail,
            linkedin: this.dataset.instructorLinkedin,
            image: this.dataset.instructorImage
        };

        // Populate popup content
        document.getElementById('popupImage').src = instructorData.image;
        document.getElementById('popupImage').alt = instructorData.name;
        document.getElementById('popupName').textContent = instructorData.name;
        document.getElementById('popupSpecialization').textContent = instructorData.specialization || '';
        document.getElementById('popupBio').textContent = instructorData.bio || 'No biography available.';

        // Handle qualifications
        const qualificationsSection = document.getElementById('popupQualificationsSection');
        const qualificationsText = document.getElementById('popupQualifications');
        if (instructorData.qualifications && instructorData.qualifications.trim()) {
            qualificationsText.textContent = instructorData.qualifications;
            qualificationsSection.style.display = 'block';
        } else {
            qualificationsSection.style.display = 'none';
        }

        // Handle achievements
        const achievementsSection = document.getElementById('popupAchievementsSection');
        const achievementsText = document.getElementById('popupAchievements');
        if (instructorData.achievements && instructorData.achievements.trim()) {
            achievementsText.textContent = instructorData.achievements;
            achievementsSection.style.display = 'block';
        } else {
            achievementsSection.style.display = 'none';
        }

        // Handle contact buttons
        const emailBtn = document.getElementById('popupEmailBtn');
        const linkedinBtn = document.getElementById('popupLinkedinBtn');

        if (instructorData.email) {
            emailBtn.href = 'mailto:' + instructorData.email;
            emailBtn.style.display = 'inline-block';
        } else {
            emailBtn.style.display = 'none';
        }

        if (instructorData.linkedin) {
            linkedinBtn.href = instructorData.linkedin;
            linkedinBtn.style.display = 'inline-block';
        } else {
            linkedinBtn.style.display = 'none';
        }

        // Show popup
        popup.style.display = 'block';
    }

    // Close popup ONLY on close button (X) click
    closeBtn.addEventListener('click', function() {
        popup.style.display = 'none';
    });

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });

    // Enhanced form field focus effects for contact form
    const formControls = document.querySelectorAll('.form-control');
    formControls.forEach(control => {
        control.addEventListener('focus', function() {
            this.style.borderColor = 'rgba(255,255,255,0.5)';
            this.style.boxShadow = '0 0 0 0.2rem rgba(255,255,255,0.25)';
        });

        control.addEventListener('blur', function() {
            if (!this.value) {
                this.style.borderColor = 'rgba(255,255,255,0.3)';
                this.style.boxShadow = 'none';
            }
        });
    });

    // Add loading state to form submission
    const contactForm = document.querySelector('form[action*="message"]');
    if (contactForm) {
        contactForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
                submitBtn.disabled = true;
            }
        });
    }
});
</script>
{% endblock %}
