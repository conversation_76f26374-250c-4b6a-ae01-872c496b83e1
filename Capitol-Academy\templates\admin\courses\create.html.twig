{% extends 'admin/base.html.twig' %}

{% block title %}Create Course - Capitol Academy Admin{% endblock %}

{% block page_title %}Create New Course{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item"><a href="{{ path('admin_dashboard') }}">Home</a></li>
<li class="breadcrumb-item"><a href="{{ path('admin_courses') }}">Courses</a></li>
<li class="breadcrumb-item active">Create Course</li>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Flash Messages -->
    {% for message in app.flashes('success') %}
        <div class="alert alert-success alert-dismissible fade show">
            <i class="fas fa-check-circle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    {% for message in app.flashes('error') %}
        <div class="alert alert-danger alert-dismissible fade show">
            <i class="fas fa-exclamation-triangle me-2"></i>{{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endfor %}

    <!-- Integrated Header with Content -->
    <div class="card border-0 shadow-lg mb-4">
        <div class="card-header" style="background: linear-gradient(135deg, #011a2d 0%, #1a3461 100%); color: white; padding: 1.5rem; border-radius: 15px 15px 0 0;">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h2 class="card-title mb-0" style="font-size: 1.8rem; font-weight: 600;">
                        <i class="fas fa-graduation-cap mr-3" style="font-size: 2rem;"></i>
                        Create New Course
                    </h2>
                </div>
                <div class="col-md-6">
                    <div class="d-flex justify-content-end align-items-center flex-wrap">
                        <!-- Back to Courses Button -->
                        <a href="{{ path('admin_courses') }}"
                           class="btn mb-2 mb-md-0"
                           style="font-weight: 600; border-radius: 8px; padding: 0.75rem 1.5rem; background: white; color: #011a2d; border: 2px solid #011a2d; transition: all 0.3s ease;"
                           onmouseover="this.style.background='#011a2d'; this.style.color='white';"
                           onmouseout="this.style.background='white'; this.style.color='#011a2d';">
                            <i class="fas fa-arrow-left me-2"></i>
                            Back to Courses
                        </a>
                    </div>
                </div>
            </div>
        </div>



        <form method="post" id="course-form" class="needs-validation" enctype="multipart/form-data" novalidate>
            <input type="hidden" name="_token" value="{{ csrf_token('course_create') }}">
            <input type="hidden" name="is_active" value="1">
            <div class="card-body">
                    <!-- Single Column Layout -->
                    <div class="row">
                        <div class="col-12">
                            <!-- Course Code and Title Row -->
                            <div class="row">
                                <!-- Course Code -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="code" class="form-label">
                                            <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Code <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="code"
                                               name="code"
                                               placeholder="e.g., TRAD101, FIN200"
                                               required
                                               maxlength="10"
                                               pattern="[A-Za-z]{2,4}[0-9]{1,4}"
                                               title="Format: 2-4 letters followed by 1-4 numbers (e.g., TRAD101, FIN200)"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a valid course code (e.g., TRAD101, FIN200).
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Title -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="title" class="form-label">
                                            <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                            Course Title <span class="text-danger">*</span>
                                        </label>
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               id="title"
                                               name="title"
                                               placeholder="Enter comprehensive course title"
                                               required
                                               maxlength="255"
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">
                                        <div class="invalid-feedback">
                                            Please provide a course title.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Course Details Row -->
                            <div class="row">
                                <!-- Course Category -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="category" class="form-label">
                                            <i class="fas fa-tags" style="color: #007bff; margin-right: 0.5rem;" aria-hidden="true"></i>
                                            Category <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="category"
                                                name="category"
                                                required
                                                aria-describedby="category_help category_error"
                                                aria-label="Select a course category"
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">
                                            <option value="">Choose a category...</option>
                                            {% for category in categories %}
                                                <option value="{{ category.name }}" {{ course.category == category.name ? 'selected' : '' }}>{{ category.name }}</option>
                                            {% endfor %}
                                        </select>
                                        <div id="category_error" class="invalid-feedback" role="alert" aria-live="polite">
                                            Please select a category.
                                        </div>
                                    </div>
                                </div>

                                <!-- Course Level -->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="level" class="form-label">
                                            <i class="fas fa-layer-group" style="color: #007bff; margin-right: 0.5rem;" aria-hidden="true"></i>
                                            Level <span class="text-danger" aria-label="required">*</span>
                                        </label>
                                        <select class="form-select enhanced-dropdown"
                                                id="level"
                                                name="level"
                                                required
                                                aria-describedby="level_help level_error"
                                                aria-label="Select a course difficulty level"
                                                style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">
                                            <option value="">Choose a level...</option>
                                            <option value="Beginner" {{ course.level == 'Beginner' ? 'selected' : '' }}>Beginner</option>
                                            <option value="Intermediate" {{ course.level == 'Intermediate' ? 'selected' : '' }}>Intermediate</option>
                                            <option value="Advanced" {{ course.level == 'Advanced' ? 'selected' : '' }}>Advanced</option>
                                        </select>
                                        <div id="level_error" class="invalid-feedback" role="alert" aria-live="polite">
                                            Please select a level.
                                        </div>
                                    </div>
                                </div>


                            </div>

                            <!-- Course Description -->
                            <div class="form-group">
                                <label for="description" class="form-label">
                                    <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Course Description <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control enhanced-field"
                                          id="description"
                                          name="description"
                                          rows="8"
                                          placeholder="Enter comprehensive course description including objectives, target audience, and key topics..."
                                          required
                                          style="min-height: 150px; font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem;">{{ course.description }}</textarea>
                                <div class="invalid-feedback">
                                    Please provide a comprehensive course description.
                                </div>
                            </div>

                            <!-- Learning Outcomes -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Learning Outcomes <span class="text-danger">*</span>
                                </label>

                                <div id="learning-outcomes-container">
                                    <div class="input-group mb-2 learning-outcome-item">
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               name="learning_outcomes[]"
                                               placeholder="e.g., Analyze market trends and identify trading opportunities"
                                               required
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn add-learning-outcome" style="background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="invalid-feedback">
                                    Please provide at least one learning outcome.
                                </div>
                            </div>

                            <!-- Course Features -->
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Course Features <span class="text-danger">*</span>
                                </label>

                                <div id="features-container">
                                    <div class="input-group mb-2 feature-item">
                                        <input type="text"
                                               class="form-control enhanced-field"
                                               name="features[]"
                                               placeholder="e.g., Live trading sessions, Real-time market analysis, Downloadable resources"
                                               required
                                               style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; border-radius: 0.375rem 0 0 0.375rem;">
                                        <div class="input-group-append">
                                            <button type="button" class="btn add-feature" style="background: #28a745; color: white; border: 2px solid #28a745; border-radius: 0 0.375rem 0.375rem 0; height: calc(1.6em + 1.25rem + 4px);">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="invalid-feedback">
                                    Please provide at least one course feature.
                                </div>
                            </div>
                            </div>

                            <!-- Course Images -->
                            <div class="form-group">
                                <label for="thumbnail_image" class="form-label">
                                    <i class="fas fa-image" style="color: #007bff; margin-right: 0.5rem;"></i>Thumbnail Image <span class="text-danger">*</span>
                                </label>
                                <input type="file"
                                       class="form-control enhanced-file-field"
                                       id="thumbnail_image"
                                       name="thumbnail_image"
                                       accept="image/jpeg,image/png,image/jpg"
                                       required
                                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da; background-color: #fff; border-radius: 0.375rem;">

                                <!-- Centered Image Preview -->
                                <div class="image-preview mt-4 text-center" id="thumbnail-preview" style="display: none;">
                                    <div class="professional-image-container mx-auto" style="width: 300px; height: 200px; border: 2px solid #011a2d; border-radius: 8px; overflow: hidden; background: #f8f9fa; box-shadow: 0 4px 12px rgba(1,26,45,0.15);">
                                        <img src="" alt="Thumbnail Preview" class="w-100 h-100" style="object-fit: cover;">
                                    </div>
                                    <small class="text-muted d-block mt-2">Thumbnail Preview (300x200px recommended)</small>
                                </div>
                            </div>

                            <!-- Has Modules -->
                            <div class="form-group" style="margin-bottom: 1.5rem;">
                                <div class="form-check form-switch" style="padding-left: 0; margin-left: 0;">
                                    <input type="checkbox"
                                           class="form-check-input"
                                           id="has_modules"
                                           name="has_modules"
                                           value="1"
                                           style="transform: scale(1.2); margin-left: 0;">
                                    <label class="form-check-label" for="has_modules" style="margin-left: 2.5rem; padding-top: 0.125rem;">
                                        <i class="fas fa-list" style="color: #007bff; margin-right: 0.5rem;"></i>
                                        <strong>Enable Course Modules</strong>
                                    </label>
                                </div>
                            </div>

                            <!-- Course Modules Management Section -->
                            <div id="modules-section" class="form-group" style="display: none;">
                                <label class="form-label">
                                    <i class="fas fa-puzzle-piece" style="color: #007bff; margin-right: 0.5rem;"></i>
                                    Course Modules
                                </label>

                                <div id="modules-container">
                                    <div class="module-item" style="border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
                                        <div class="module-header d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0" style="color: #007bff; font-weight: 700;">
                                                <i class="fas fa-cube" style="margin-right: 0.5rem;"></i>
                                                Module <span class="module-number">1</span>
                                            </h6>
                                            <button type="button" class="btn btn-sm btn-outline-danger remove-module" style="border-radius: 50%; width: 35px; height: 35px;">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>

                                        <!-- Module Basic Info -->
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">
                                                        <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                        Module Code
                                                    </label>
                                                    <input type="text" class="form-control enhanced-field module-code" name="modules[0][code]" placeholder="e.g., Module-1">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label class="form-label">
                                                        <i class="fas fa-tag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                        Module Title <span class="text-danger">*</span>
                                                    </label>
                                                    <input type="text" class="form-control enhanced-field module-title" name="modules[0][title]" placeholder="e.g., Introduction to Trading Fundamentals" required>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Module Description -->
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                Module Description <span class="text-danger">*</span>
                                            </label>
                                            <textarea class="form-control enhanced-field module-description" name="modules[0][description]" rows="6" placeholder="Detailed description of what this module covers..." required style="min-height: 100px;"></textarea>
                                        </div>



                                        <!-- Module Learning Outcomes -->
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                Learning Outcomes <span class="text-danger">*</span>
                                            </label>
                                            <div class="learning-outcomes-container">
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control enhanced-field" name="modules[0][learning_outcomes][]" placeholder="e.g., Master advanced chart analysis techniques" required style="border-radius: 0.375rem 0 0 0.375rem;">
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Module Features -->
                                        <div class="form-group">
                                            <label class="form-label">
                                                <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                                                Features <span class="text-danger">*</span>
                                            </label>
                                            <div class="features-container">
                                                <div class="input-group mb-2">
                                                    <input type="text" class="form-control enhanced-field" name="modules[0][features][]" placeholder="e.g., Interactive trading simulator" required style="border-radius: 0.375rem 0 0 0.375rem;">
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                                            <i class="fas fa-plus"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Hidden fields -->
                                        <input type="hidden" name="modules[0][is_active]" value="1">
                                        <input type="hidden" name="modules[0][sort_order]" value="1">
                                    </div>
                                </div>

                                <button type="button" id="add-module" class="btn btn-outline-primary">
                                    <i class="fas fa-plus" style="margin-right: 0.5rem;"></i>
                                    Add Another Module
                                </button>
                            </div>

                        </div>

                        <!-- Action Buttons Section -->
                        <div class="form-footer" style="background: #f8f9fa; border-top: 2px solid #e9ecef; padding: 2rem; margin: 2rem -2rem -2rem -2rem; border-radius: 0 0 12px 12px;">
                            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                                <button type="submit" class="btn btn-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; border: none; font-weight: 700; border-radius: 8px; padding: 1rem 2.5rem; transition: all 0.3s ease; min-width: 200px; margin-right: 1rem; margin-bottom: 1rem; box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);">
                                    <i class="fas fa-save mr-2"></i>
                                    Create Course
                                </button>
                                <a href="{{ path('admin_courses') }}" class="btn btn-lg" style="background: #6c757d; color: white; border: none; font-weight: 600; border-radius: 8px; padding: 1rem 2.5rem; min-width: 200px; margin-left: 1rem; margin-bottom: 1rem; transition: all 0.3s ease; text-decoration: none;">
                                    <i class="fas fa-times mr-2"></i>
                                    Cancel
                                </a>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- Professional Module Removal Modal -->
<div class="modal fade" id="courseValidationModal" tabindex="-1" aria-labelledby="courseValidationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-header" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; border: none;">
                <h5 class="modal-title" id="courseValidationModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Cannot Remove Module
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-info-circle text-danger" style="font-size: 3rem;"></i>
                </div>
                <h6 class="mt-3 mb-2">Module Required</h6>
                <p class="text-muted mb-3"><strong>Warning:</strong> A course must have at least one module.</p>
            </div>
            <div class="modal-footer border-0 justify-content-center">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">
                    <i class="fas fa-check me-2"></i>Understood
                </button>
            </div>
        </div>
    </div>
</div>
    <!-- Module Validation Modal -->
    <div class="modal fade" id="moduleValidationModal" tabindex="-1" aria-labelledby="moduleValidationModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-sm">
            <div class="modal-content" style="border: none; border-radius: 8px; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);">
                <div class="modal-header" style="background: linear-gradient(135deg, #a90418 0%, #8b0314 100%); color: white; border: none; padding: 1rem;">
                    <h6 class="modal-title" id="moduleValidationModalLabel" style="font-weight: 600;">
                        <i class="fas fa-exclamation-triangle me-2"></i>Module Required
                    </h6>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="padding: 1rem; text-align: center;">
                    <p class="mb-3" style="color: #a90418;">
                        At least one module is required.
                    </p>
                    <small class="text-muted">Please add a module or disable the "Enable Course Modules" option.</small>
                </div>
                <div class="modal-footer" style="border: none; padding: 1rem; background: #f8f9fa;">
                    <button type="button" class="btn btn-primary btn-sm" data-bs-dismiss="modal" style="transition: all 0.3s ease;">
                        <i class="fas fa-check me-1"></i>OK
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
<script>
$(document).ready(function() {
    // Function to toggle module field validation based on "Course Has Modules" state
    function toggleModuleValidation() {
        var hasModules = $('#has_modules').is(':checked');
        var moduleInputs = $('#modules-section input[name*="modules"], #modules-section textarea[name*="modules"]');

        if (hasModules) {
            // Enable validation for module fields
            moduleInputs.each(function() {
                $(this).attr('required', true);
            });
        } else {
            // Disable validation for module fields
            moduleInputs.each(function() {
                $(this).removeAttr('required');
                // Clear any validation state
                $(this).removeClass('is-invalid is-valid');
                $(this).closest('.form-group').find('.invalid-feedback').hide();
            });
        }
    }

    // Bootstrap form validation
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('needs-validation');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    console.log('Form submission attempted');

                    var submitBtn = form.querySelector('button[type="submit"]');

                    // Update module validation based on toggle state before validation
                    toggleModuleValidation();

                    // Check if form is valid using native HTML5 validation
                    if (form.checkValidity() === false) {
                        console.log('Form validation failed');
                        event.preventDefault();
                        event.stopPropagation();

                        // Reset button state on validation failure
                        if (submitBtn) {
                            submitBtn.disabled = false;
                            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Create Course';
                        }

                        // Show help text when validation fails
                        $('.help-text').show();

                        // Add validation classes for styling only when validation fails
                        form.classList.add('was-validated');
                    } else {
                        console.log('Form validation passed, submitting...');
                        // Show loading state on successful validation
                        if (submitBtn) {
                            submitBtn.disabled = true;
                            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Creating Course...';
                        }

                        // Hide help text when form is valid
                        $('.help-text').hide();

                        // Remove validation classes when form is valid
                        form.classList.remove('was-validated');
                    }
                }, false);
            });
        }, false);
    })();

    // Initialize module validation state on page load
    $(document).ready(function() {
        toggleModuleValidation();
    });

    // Auto-generate course code suggestion
    $('#title').on('input', function() {
        var title = $(this).val();
        if (title.length > 0 && $('#code').val() === '') {
            var words = title.split(' ');
            var suggestion = '';
            if (words.length >= 2) {
                suggestion = words[0].substring(0, 2).toUpperCase() + 
                           words[1].substring(0, 1).toUpperCase() + 
                           '101';
            } else {
                suggestion = title.substring(0, 3).toUpperCase() + '101';
            }
            $('#code').attr('placeholder', 'Suggestion: ' + suggestion);
        }
    });



    // Form enhancement animations
    $('.form-control').on('focus', function() {
        $(this).closest('.form-group').addClass('focused');
    }).on('blur', function() {
        $(this).closest('.form-group').removeClass('focused');
    });

    // Dynamic Learning Outcomes Management
    $(document).on('click', '.add-learning-outcome', function() {
        var container = $('#learning-outcomes-container');
        var newItem = `
            <div class="input-group mb-2 learning-outcome-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="learning_outcomes[]"
                       placeholder="Enter a learning outcome..."
                       required
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-learning-outcome">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-learning-outcome">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    $(document).on('click', '.remove-learning-outcome', function() {
        $(this).closest('.learning-outcome-item').remove();
    });

    // Dynamic Features Management
    $(document).on('click', '.add-feature', function() {
        var container = $('#features-container');
        var newItem = `
            <div class="input-group mb-2 feature-item">
                <input type="text"
                       class="form-control enhanced-field"
                       name="features[]"
                       placeholder="e.g., Live instructor sessions, Downloadable resources..."
                       style="height: calc(1.6em + 1.25rem + 4px); font-size: 1rem; border: 2px solid #ced4da;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-feature">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-feature">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(newItem);
    });

    $(document).on('click', '.remove-feature', function() {
        $(this).closest('.feature-item').remove();
    });

    // Image Preview Functionality
    $('#thumbnail_image').on('change', function() {
        previewImage(this, '#thumbnail-preview');
    });



    function previewImage(input, previewSelector) {
        if (input.files && input.files[0]) {
            var reader = new FileReader();
            reader.onload = function(e) {
                $(previewSelector).show();
                $(previewSelector + ' img').attr('src', e.target.result);
            };
            reader.readAsDataURL(input.files[0]);
        } else {
            $(previewSelector).hide();
        }
    }

    // Module management
    var moduleIndex = 1;

    // Toggle module section visibility
    $('#has_modules').on('change', function() {
        if ($(this).is(':checked')) {
            $('#modules-section').slideDown();
        } else {
            $('#modules-section').slideUp();
        }
        toggleModuleValidation();
    });

    // Function to toggle module field validation based on "Course Has Modules" state
    function toggleModuleValidation() {
        var hasModules = $('#has_modules').is(':checked');
        var moduleInputs = $('#modules-section input[name*="modules"], #modules-section textarea[name*="modules"]');

        if (hasModules) {
            moduleInputs.attr('required', true);
        } else {
            moduleInputs.removeAttr('required');
        }
    }

    $('#add-module').on('click', function() {
        var newModule = `
            <div class="module-item" style="border: 2px solid #007bff; border-radius: 12px; padding: 1.5rem; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);">
                <div class="module-header d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0" style="color: #007bff; font-weight: 700;">
                        <i class="fas fa-cube" style="margin-right: 0.5rem;"></i>
                        Module <span class="module-number">${moduleIndex + 1}</span>
                    </h6>
                    <button type="button" class="btn btn-sm btn-outline-danger remove-module" style="border-radius: 50%; width: 35px; height: 35px;">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Module Basic Info -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-hashtag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                Module Code
                            </label>
                            <input type="text" class="form-control enhanced-field module-code" name="modules[${moduleIndex}][code]" placeholder="e.g., Module-${moduleIndex + 1}">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-tag" style="color: #007bff; margin-right: 0.5rem;"></i>
                                Module Title <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control enhanced-field module-title" name="modules[${moduleIndex}][title]" placeholder="e.g., Advanced Trading Strategies" required>
                        </div>
                    </div>
                </div>

                <!-- Module Description -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-align-left" style="color: #007bff; margin-right: 0.5rem;"></i>
                        Module Description <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control enhanced-field module-description" name="modules[${moduleIndex}][description]" rows="6" placeholder="Detailed description of what this module covers..." required style="min-height: 150px;"></textarea>
                </div>



                <!-- Module Learning Outcomes -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-graduation-cap" style="color: #007bff; margin-right: 0.5rem;"></i>
                        Learning Outcomes <span class="text-danger">*</span>
                    </label>
                    <div class="learning-outcomes-container">
                        <div class="input-group mb-2">
                            <input type="text" class="form-control enhanced-field" name="modules[${moduleIndex}][learning_outcomes][]" placeholder="e.g., Master advanced chart analysis techniques" required style="border-radius: 0.375rem 0 0 0.375rem;">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Module Features -->
                <div class="form-group">
                    <label class="form-label">
                        <i class="fas fa-star" style="color: #007bff; margin-right: 0.5rem;"></i>
                        Features <span class="text-danger">*</span>
                    </label>
                    <div class="features-container">
                        <div class="input-group mb-2">
                            <input type="text" class="form-control enhanced-field" name="modules[${moduleIndex}][features][]" placeholder="e.g., Interactive trading simulator" required style="border-radius: 0.375rem 0 0 0.375rem;">
                            <div class="input-group-append">
                                <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                                    <i class="fas fa-plus"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Hidden fields -->
                <input type="hidden" name="modules[${moduleIndex}][is_active]" value="1">
                <input type="hidden" name="modules[${moduleIndex}][sort_order]" value="${moduleIndex + 1}">
            </div>
        `;

        $('#modules-container').append(newModule);
        moduleIndex++;
        updateModuleNumbers();
        toggleModuleValidation();
    });

    // Remove module
    $(document).on('click', '.remove-module', function() {
        if ($('.module-item').length > 1) {
            $(this).closest('.module-item').remove();
            updateModuleNumbers();
        } else {
            const modal = new bootstrap.Modal(document.getElementById('moduleValidationModal'));
            modal.show();
        }
    });

    // Add learning outcome
    $(document).on('click', '.add-outcome', function() {
        var container = $(this).closest('.learning-outcomes-container');
        var moduleIndex = container.closest('.module-item').index();
        var outcomeHtml = `
            <div class="input-group mb-2">
                <input type="text" class="form-control enhanced-field" name="modules[${moduleIndex}][learning_outcomes][]" placeholder="Enter another learning outcome..." style="border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-outcome" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(outcomeHtml);
    });

    // Remove learning outcome
    $(document).on('click', '.remove-outcome', function() {
        $(this).closest('.input-group').remove();
    });

    // Add feature
    $(document).on('click', '.add-feature', function() {
        var container = $(this).closest('.features-container');
        var moduleIndex = container.closest('.module-item').index();
        var featureHtml = `
            <div class="input-group mb-2">
                <input type="text" class="form-control enhanced-field" name="modules[${moduleIndex}][features][]" placeholder="Enter another feature..." style="border-radius: 0.375rem 0 0 0.375rem;">
                <div class="input-group-append">
                    <button type="button" class="btn btn-danger remove-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0;">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="btn btn-success add-feature" style="height: calc(1.6em + 1.25rem + 4px); border-radius: 0 0.375rem 0.375rem 0;">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
        `;
        container.append(featureHtml);
    });

    // Remove feature
    $(document).on('click', '.remove-feature', function() {
        $(this).closest('.input-group').remove();
    });

    // Update module numbers
    function updateModuleNumbers() {
        $('.module-item').each(function(index) {
            $(this).find('.module-number').text(index + 1);
            // Update form field names
            $(this).find('input, textarea, select').each(function() {
                var name = $(this).attr('name');
                if (name && name.includes('modules[')) {
                    var newName = name.replace(/modules\[\d+\]/, 'modules[' + index + ']');
                    $(this).attr('name', newName);
                }
            });
        });
    }



    // Form validation
    $('#course-form').on('submit', function(e) {
        var hasModules = $('#has_modules').is(':checked');
        var moduleCount = $('.module-item').length;

        if (hasModules && moduleCount === 0) {
            e.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('moduleValidationModal'));
            modal.show();
            return false;
        }

        // Update module validation before form submission
        toggleModuleValidation();

        // Validate all required fields
        var isValid = true;
        $(this).find('input[required], textarea[required], select[required]').each(function() {
            if (!$(this).val()) {
                isValid = false;
                $(this).addClass('is-invalid');
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields.');
        }
    });
});
</script>

<style>
.form-group.focused .form-label {
    color: #1e3c72;
    font-weight: 600;
}

.form-group.focused .form-control {
    border-color: #1e3c72;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25);
}

.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #28a745;
    border-color: #28a745;
}

.card-header {
    position: relative;
    overflow: hidden;
}

.card-header::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
    transform: rotate(45deg);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

/* Fix dropdown display issues */
.form-select {
    padding: 0.75rem 1rem !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    height: auto !important;
    min-height: 48px !important;
    background-position: right 0.75rem center !important;
    background-size: 16px 12px !important;
    border: 1px solid #ced4da !important;
    border-radius: 0.375rem !important;
}

.form-select option {
    padding: 8px 12px !important;
    font-size: 1rem !important;
    line-height: 1.5 !important;
    color: #212529 !important;
    background-color: #fff !important;
}

.form-select:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

/* Enhanced Form Field Styling */
.enhanced-field {
    transition: all 0.3s ease;
    font-size: 1rem !important;
    border: 2px solid #ced4da !important;
    height: calc(1.6em + 1.25rem + 4px) !important;
    border-radius: 0.375rem !important;
}

.enhanced-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
}

.enhanced-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

.enhanced-dropdown {
    transition: all 0.3s ease;
    font-size: 1rem !important;
    border: 2px solid #ced4da !important;
    background-color: #fff !important;
    cursor: pointer;
    font-weight: 500;
}

.enhanced-dropdown:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    background-color: #f8f9fa !important;
}

.enhanced-dropdown:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    outline: none;
    background-color: #fff !important;
}

/* Enhanced dropdown option styling for better readability */
.enhanced-dropdown option {
    padding: 0.75rem 1rem;
    background-color: #fff;
    color: #011a2d;
    font-weight: 500;
    line-height: 1.5;
    border-bottom: 1px solid #f1f3f4;
}

.enhanced-dropdown option:first-child {
    color: #6c757d;
    font-style: italic;
    font-weight: 400;
}

.enhanced-dropdown option:not(:first-child) {
    color: #011a2d;
    font-weight: 500;
}

.enhanced-dropdown option:hover {
    background-color: #f8f9fa;
    color: #1e3c72;
}

.enhanced-dropdown option:checked,
.enhanced-dropdown option:selected {
    background-color: #1e3c72;
    color: white;
    font-weight: 600;
}

.enhanced-file-field {
    transition: all 0.3s ease;
    font-size: 1rem !important;
    border: 2px solid #ced4da !important;
    background-color: #fff !important;
    position: relative;
    cursor: pointer;
}

.enhanced-file-field:hover {
    border-color: #1e3c72 !important;
    box-shadow: 0 2px 8px rgba(30, 60, 114, 0.15) !important;
    transform: translateY(-1px);
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;
}

.enhanced-file-field:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
    transform: translateY(-1px);
}

/* Custom File Upload Button Styling */
.enhanced-file-field::file-selector-button {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    margin-right: 1rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.enhanced-file-field::file-selector-button:hover {
    background: linear-gradient(135deg, #a90418 0%, #dc3545 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Webkit browsers (Chrome, Safari, Edge) */
.enhanced-file-field::-webkit-file-upload-button {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    margin-right: 1rem;
    border-radius: 6px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.enhanced-file-field::-webkit-file-upload-button:hover {
    background: linear-gradient(135deg, #a90418 0%, #dc3545 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(169, 4, 24, 0.3);
}

/* Professional Image Preview Styling */
.professional-image-container {
    transition: all 0.3s ease;
}

.professional-image-container:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.2) !important;
}

/* Module Card Enhancement */
.module-item {
    transition: all 0.3s ease;
}

.module-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1) !important;
}

/* Select2 Custom Styling - Force consistent height with other form fields */
.select2-container--bootstrap4 .select2-selection--single {
    height: calc(1.6em + 1.25rem + 4px) !important;
    min-height: calc(1.6em + 1.25rem + 4px) !important;
    border: 2px solid #ced4da !important;
    border-radius: 8px !important;
    font-size: 1rem !important;
    box-sizing: border-box !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__rendered {
    padding-left: 12px !important;
    padding-right: 12px !important;
    line-height: calc(1.6em + 1.25rem) !important;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
}

.select2-container--bootstrap4 .select2-selection--single .select2-selection__arrow {
    height: calc(1.6em + 1.25rem) !important;
    top: 2px !important;
    right: 2px !important;
}

.select2-container--bootstrap4 .select2-selection--single:focus {
    border-color: #1e3c72 !important;
    box-shadow: 0 0 0 0.2rem rgba(30, 60, 114, 0.25) !important;
}

.select2-container--bootstrap4 .select2-dropdown {
    border: 2px solid #1e3c72 !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(30, 60, 114, 0.15) !important;
}

.select2-container--bootstrap4 .select2-results__option--highlighted {
    background-color: #1e3c72 !important;
    color: white !important;
}

/* Ensure Select2 containers match form field heights exactly */
.select2-container {
    height: calc(1.6em + 1.25rem + 4px) !important;
}

.select2-container .select2-selection {
    height: calc(1.6em + 1.25rem + 4px) !important;
}
</style>

<!-- Include Select2 for enhanced dropdowns -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/@ttskch/select2-bootstrap4-theme@x.x.x/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
$(document).ready(function() {
    // Enhanced category selection with search functionality
    const categorySelect = document.getElementById('category');
    if (categorySelect) {
        $(categorySelect).select2({
            placeholder: 'Search and select a category...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }

    // Enhanced level selection with search functionality
    const levelSelect = document.getElementById('level');
    if (levelSelect) {
        $(levelSelect).select2({
            placeholder: 'Search and select a level...',
            allowClear: true,
            width: '100%',
            theme: 'bootstrap4'
        });
    }
});
</script>
{% endblock %}
