<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Remove course_review and plan_video tables from database
 */
final class Version20250717000001 extends AbstractMigration
{
    public function getDescription(): string
    {
        return 'Remove course_review and plan_video tables from database';
    }

    public function up(Schema $schema): void
    {
        // Drop course_review table if it exists
        $this->addSql('DROP TABLE IF EXISTS course_review');
        
        // Drop plan_video table if it exists (old table name)
        $this->addSql('DROP TABLE IF EXISTS plan_video');
        
        // Drop plan_videos table if it exists (new table name)
        $this->addSql('DROP TABLE IF EXISTS plan_videos');
    }

    public function down(Schema $schema): void
    {
        // Recreate course_review table
        $this->addSql('CREATE TABLE course_review (
            id INT AUTO_INCREMENT NOT NULL, 
            course_id INT NOT NULL, 
            user_id INT NOT NULL, 
            rating SMALLINT NOT NULL, 
            comment LONGTEXT DEFAULT NULL, 
            is_certified TINYINT(1) NOT NULL, 
            is_approved TINYINT(1) NOT NULL, 
            is_featured TINYINT(1) NOT NULL, 
            created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', 
            INDEX IDX_D77B408B591CC992 (course_id), 
            INDEX IDX_D77B408BA76ED395 (user_id), 
            PRIMARY KEY(id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add foreign key constraints for course_review table
        $this->addSql('ALTER TABLE course_review ADD CONSTRAINT FK_D77B408B591CC992 FOREIGN KEY (course_id) REFERENCES course (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE course_review ADD CONSTRAINT FK_D77B408BA76ED395 FOREIGN KEY (user_id) REFERENCES user (id) ON DELETE CASCADE');
        
        // Recreate plan_videos table
        $this->addSql('CREATE TABLE plan_videos (
            plan_id INT NOT NULL,
            video_id INT NOT NULL,
            INDEX IDX_PLAN_VIDEOS_PLAN (plan_id),
            INDEX IDX_PLAN_VIDEOS_VIDEO (video_id),
            PRIMARY KEY(plan_id, video_id)
        ) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');

        // Add foreign key constraints for plan_videos table
        $this->addSql('ALTER TABLE plan_videos ADD CONSTRAINT FK_PLAN_VIDEOS_PLAN FOREIGN KEY (plan_id) REFERENCES plan (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE plan_videos ADD CONSTRAINT FK_PLAN_VIDEOS_VIDEO FOREIGN KEY (video_id) REFERENCES video (id) ON DELETE CASCADE');
    }
}
